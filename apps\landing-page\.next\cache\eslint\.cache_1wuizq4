[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18"}, {"size": 14717, "mtime": 1749600160030, "results": "19", "hashOfConfig": "20"}, {"size": 9016, "mtime": 1749596942009, "results": "21", "hashOfConfig": "20"}, {"size": 385, "mtime": 1749589496137, "results": "22", "hashOfConfig": "20"}, {"size": 12528, "mtime": 1749599018277, "results": "23", "hashOfConfig": "20"}, {"size": 21668, "mtime": 1749600046512, "results": "24", "hashOfConfig": "20"}, {"size": 13041, "mtime": 1749598961324, "results": "25", "hashOfConfig": "20"}, {"size": 13776, "mtime": 1749598503640, "results": "26", "hashOfConfig": "20"}, {"size": 9920, "mtime": 1749599065660, "results": "27", "hashOfConfig": "20"}, {"size": 15995, "mtime": 1749600416171, "results": "28", "hashOfConfig": "20"}, {"size": 10063, "mtime": 1749599322400, "results": "29", "hashOfConfig": "20"}, {"size": 14628, "mtime": 1749599271491, "results": "30", "hashOfConfig": "20"}, {"size": 3319, "mtime": 1749593558895, "results": "31", "hashOfConfig": "20"}, {"size": 2575, "mtime": 1749598342857, "results": "32", "hashOfConfig": "20"}, {"size": 1646, "mtime": 1749589446276, "results": "33", "hashOfConfig": "20"}, {"size": 2677, "mtime": 1749597314087, "results": "34", "hashOfConfig": "20"}, {"size": 2428, "mtime": 1749596227387, "results": "35", "hashOfConfig": "20"}, {"size": 989, "mtime": 1749596212722, "results": "36", "hashOfConfig": "20"}, {"size": 2120, "mtime": 1749597019188, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ody2rz", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", ["92"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", ["93", "94"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", ["95", "96"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", ["97"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", ["98"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["99"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["100", "101", "102", "103", "104", "105", "106", "107"], [], {"ruleId": "108", "severity": 2, "message": "109", "line": 311, "column": 28, "nodeType": "110", "messageId": "111", "endLine": 311, "endColumn": 33}, {"ruleId": "108", "severity": 2, "message": "112", "line": 11, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 11, "endColumn": 15}, {"ruleId": "108", "severity": 2, "message": "113", "line": 12, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 12, "endColumn": 19}, {"ruleId": "108", "severity": 2, "message": "112", "line": 15, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 15, "endColumn": 15}, {"ruleId": "108", "severity": 2, "message": "113", "line": 16, "column": 3, "nodeType": "110", "messageId": "111", "endLine": 16, "endColumn": 19}, {"ruleId": null, "fatal": true, "severity": 2, "message": "114", "line": 326, "column": 12, "nodeType": null}, {"ruleId": "108", "severity": 2, "message": "112", "line": 6, "column": 31, "nodeType": "110", "messageId": "111", "endLine": 6, "endColumn": 43}, {"ruleId": "115", "severity": 1, "message": "116", "line": 37, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 37, "endColumn": 20, "suggestions": "119", "suppressions": "120"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 7, "column": 30, "nodeType": "117", "messageId": "118", "endLine": 7, "endColumn": 43}, {"ruleId": "115", "severity": 1, "message": "116", "line": 8, "column": 29, "nodeType": "117", "messageId": "118", "endLine": 8, "endColumn": 41}, {"ruleId": "115", "severity": 1, "message": "116", "line": 37, "column": 5, "nodeType": "117", "messageId": "118", "endLine": 37, "endColumn": 18}, {"ruleId": "121", "severity": 1, "message": "122", "line": 37, "column": 31, "nodeType": "123", "messageId": "124", "endLine": 37, "endColumn": 34, "suggestions": "125"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 44, "column": 5, "nodeType": "117", "messageId": "118", "endLine": 44, "endColumn": 17}, {"ruleId": "121", "severity": 1, "message": "122", "line": 44, "column": 30, "nodeType": "123", "messageId": "124", "endLine": 44, "endColumn": 33, "suggestions": "126"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 73, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 73, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "116", "line": 74, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 74, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'index' is defined but never used. Allowed unused args must match /^_/u.", "Identifier", "unusedVar", "'SparklesIcon' is defined but never used.", "'RocketLaunchIcon' is defined but never used.", "Parsing error: Expected corresponding JSX closing tag for 'motion.div'.", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["127"], ["128"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["129", "130"], ["131", "132"], {"messageId": "133", "data": "134", "fix": "135", "desc": "136"}, {"kind": "137", "justification": "138"}, {"messageId": "139", "fix": "140", "desc": "141"}, {"messageId": "142", "fix": "143", "desc": "144"}, {"messageId": "139", "fix": "145", "desc": "141"}, {"messageId": "142", "fix": "146", "desc": "144"}, "removeConsole", {"propertyName": "147"}, {"range": "148", "text": "138"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "149", "text": "150"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "151", "text": "152"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "153", "text": "150"}, {"range": "154", "text": "152"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]