"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvelopeIcon: function() { return /* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   PaperAirplaneIcon: function() { return /* reexport safe */ _PaperAirplaneIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   SparklesIcon: function() { return /* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _PaperAirplaneIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PaperAirplaneIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SparklesIcon.js */ \"../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FbnZlbG9wZUljb24sUGFwZXJBaXJwbGFuZUljb24sU3BhcmtsZXNJY29uIT0hLi4vLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMyRDtBQUNVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9jNzE1Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFbnZlbG9wZUljb24gfSBmcm9tIFwiLi9FbnZlbG9wZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQYXBlckFpcnBsYW5lSWNvbiB9IGZyb20gXCIuL1BhcGVyQWlycGxhbmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3BhcmtsZXNJY29uIH0gZnJvbSBcIi4vU3BhcmtsZXNJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./src/components/sections/Newsletter.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Newsletter.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Newsletter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"../../node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Validation schema\nconst newsletterSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email(\"Invalid email address\")\n});\nfunction Newsletter() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(newsletterSchema)\n    });\n    const onSubmit = async (_data)=>{\n        setIsSubmitting(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // In a real app, you would send the email to your newsletter service\n            // TODO: Send email to newsletter service\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(t(\"newsletter.success\"));\n            reset();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(t(\"newsletter.error\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"newsletter\",\n        ref: ref,\n        className: \"section-padding relative overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(206, 17, 38, 0.15) 0%, transparent 60%),\\n          radial-gradient(circle at \").concat(100 - mousePosition.x, \"% \").concat(100 - mousePosition.y, \"%, rgba(0, 122, 61, 0.12) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -30,\n                                30,\n                                -30\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 w-32 h-32 rounded-full opacity-20\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                40,\n                                -40,\n                                40\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-40 right-20 w-24 h-24 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(15px)\",\n                            WebkitBackdropFilter: \"blur(15px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -25,\n                                25,\n                                -25\n                            ],\n                            x: [\n                                -10,\n                                10,\n                                -10\n                            ],\n                            rotate: [\n                                0,\n                                90,\n                                180\n                            ]\n                        },\n                        transition: {\n                            duration: 18,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-40 left-20 w-40 h-40 rounded-full opacity-10\",\n                        style: {\n                            background: \"rgba(124, 58, 237, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(124, 58, 237, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(124, 58, 237, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            animate: {\n                                y: [\n                                    -20,\n                                    20,\n                                    -20\n                                ],\n                                x: [\n                                    -10,\n                                    10,\n                                    -10\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i * 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.5\n                            },\n                            className: \"absolute w-2 h-2 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(10 + i * 12, \"%\"),\n                                top: \"\".concat(20 + i * 8, \"%\"),\n                                filter: \"blur(1px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"relative mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"w-24 h-24 mx-auto rounded-full flex items-center justify-center relative z-10\",\n                                whileHover: {\n                                    scale: 1.1,\n                                    rotate: 5\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                style: {\n                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)\",\n                                    backdropFilter: \"blur(20px)\",\n                                    WebkitBackdropFilter: \"blur(20px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.EnvelopeIcon, {\n                                        className: \"w-12 h-12 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.SparklesIcon, {\n                                        className: \"w-6 h-6 text-white absolute -top-2 -right-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"relative mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                    className: \"heading-lg text-white mb-4 relative z-10 px-8 py-4 text-arabic-premium\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    children: t(\"newsletter.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-2xl opacity-30\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)\",\n                                        backdropFilter: \"blur(30px)\",\n                                        WebkitBackdropFilter: \"blur(30px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                        boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                            variants: itemVariants,\n                            className: \"text-xl text-white/90 mb-16 max-w-3xl mx-auto text-center leading-relaxed text-arabic px-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            children: t(\"newsletter.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"email\"),\n                                                    type: \"email\",\n                                                    placeholder: t(\"newsletter.placeholder\"),\n                                                    className: \"w-full px-6 py-4 rounded-lg bg-white/95 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white focus:outline-none transition-all duration-200 \".concat(isRTL ? \"text-right\" : \"text-left\"),\n                                                    disabled: isSubmitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/90 text-sm mt-2 text-start\",\n                                                    children: t(\"common.validation.email\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"w-full bg-white text-primary-600 hover:bg-gray-50 font-medium px-6 py-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.PaperAirplaneIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isSubmitting ? t(\"common.status.loading\") : t(\"newsletter.submit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/70 text-sm mt-6\",\n                                    children: isRTL ? \"لن نشارك بريدك الإلكتروني مع أي طرف ثالث. يمكنك إلغاء الاشتراك في أي وقت.\" : \"We won't share your email with anyone else. You can unsubscribe at any time.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCE7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"أخبار أسبوعية\" : \"Weekly Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"احصل على آخر الأخبار والفرص كل أسبوع\" : \"Get the latest news and opportunities every week\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"فرص حصرية\" : \"Exclusive Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"كن أول من يعلم بالفرص الجديدة\" : \"Be the first to know about new opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCA1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"نصائح مفيدة\" : \"Helpful Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"نصائح لتطوير مهاراتك وزيادة دخلك\" : \"Tips to develop your skills and increase your income\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(Newsletter, \"apw5LViUSGeGus4dp0ePtWuFBDU=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = Newsletter;\nvar _c;\n$RefreshReg$(_c, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9OZXdzbGV0dGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNFO0FBQ047QUFDRDtBQUNpQjtBQUNkO0FBQ1k7QUFDOUI7QUFDWTtBQUN3RDtBQUU1RixvQkFBb0I7QUFDcEIsTUFBTWEsbUJBQW1CTCxrQ0FBQ0EsQ0FBQ00sTUFBTSxDQUFDO0lBQ2hDQyxPQUFPUCxrQ0FBQ0EsQ0FBQ1EsTUFBTSxHQUFHRCxLQUFLLENBQUM7QUFDMUI7QUFJZSxTQUFTRTs7SUFDdEIsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR2hCLDREQUFjQSxDQUFDO0lBQzdCLE1BQU1pQixTQUFTaEIsc0RBQVNBO0lBQ3hCLE1BQU0sRUFBRWlCLE1BQU0sRUFBRSxHQUFHRDtJQUNuQixNQUFNRSxRQUFRRCxXQUFXO0lBRXpCLE1BQU0sQ0FBQ0UsY0FBY0MsZ0JBQWdCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN3QixlQUFlQyxpQkFBaUIsR0FBR3pCLCtDQUFRQSxDQUFDO1FBQUUwQixHQUFHO1FBQUdDLEdBQUc7SUFBRTtJQUVoRSwrQ0FBK0M7SUFDL0MxQixnREFBU0EsQ0FBQztRQUNSLE1BQU0yQixrQkFBa0IsQ0FBQ0M7WUFDdkJKLGlCQUFpQjtnQkFDZkMsR0FBRyxFQUFHSSxPQUFPLEdBQUdDLE9BQU9DLFVBQVUsR0FBSTtnQkFDckNMLEdBQUcsRUFBR00sT0FBTyxHQUFHRixPQUFPRyxXQUFXLEdBQUk7WUFDeEM7UUFDRjtRQUVBSCxPQUFPSSxnQkFBZ0IsQ0FBQyxhQUFhUDtRQUNyQyxPQUFPLElBQU1HLE9BQU9LLG1CQUFtQixDQUFDLGFBQWFSO0lBQ3ZELEdBQUcsRUFBRTtJQUVMLE1BQU0sQ0FBQ1MsS0FBS0MsT0FBTyxHQUFHakMsc0VBQVNBLENBQUM7UUFDOUJrQyxhQUFhO1FBQ2JDLFdBQVc7SUFDYjtJQUVBLE1BQU0sRUFDSkMsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLEtBQUssRUFDTEMsV0FBVyxFQUFFQyxNQUFNLEVBQUUsRUFDdEIsR0FBR3ZDLHdEQUFPQSxDQUFxQjtRQUM5QndDLFVBQVV2QyxvRUFBV0EsQ0FBQ007SUFDeEI7SUFFQSxNQUFNa0MsV0FBVyxPQUFPQztRQUN0QnpCLGdCQUFnQjtRQUVoQixJQUFJO1lBQ0Ysb0JBQW9CO1lBQ3BCLE1BQU0sSUFBSTBCLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFFakQscUVBQXFFO1lBQ3JFLHlDQUF5QztZQUV6Q3pDLCtEQUFhLENBQUNTLEVBQUU7WUFDaEJ5QjtRQUNGLEVBQUUsT0FBT1UsT0FBTztZQUNkNUMsNkRBQVcsQ0FBQ1MsRUFBRTtRQUNoQixTQUFVO1lBQ1JLLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsTUFBTStCLG9CQUFvQjtRQUN4QkMsUUFBUTtZQUFFQyxTQUFTO1FBQUU7UUFDckJDLFNBQVM7WUFDUEQsU0FBUztZQUNURSxZQUFZO2dCQUNWQyxpQkFBaUI7WUFDbkI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsZUFBZTtRQUNuQkwsUUFBUTtZQUFFQyxTQUFTO1lBQUc3QixHQUFHO1FBQUc7UUFDNUI4QixTQUFTO1lBQ1BELFNBQVM7WUFDVDdCLEdBQUc7WUFDSCtCLFlBQVk7Z0JBQ1ZHLFVBQVU7Z0JBQ1ZDLE1BQU07WUFDUjtRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsSUFBRztRQUNIM0IsS0FBS0E7UUFDTDRCLFdBQVU7UUFDVkMsT0FBTztZQUNMQyxZQUFZLHlDQUNzQzNDLE9BQXBCQSxjQUFjRSxDQUFDLEVBQUMsTUFDaEIsT0FEb0JGLGNBQWNHLENBQUMsRUFBQywwRkFDVixPQUExQixNQUFNSCxjQUFjRSxDQUFDLEVBQUMsTUFBMEIsT0FBdEIsTUFBTUYsY0FBY0csQ0FBQyxFQUFDO1FBR2hGOzswQkFHQSw4REFBQ3lDO2dCQUFJSCxXQUFVOztrQ0FFYiw4REFBQzdELGlEQUFNQSxDQUFDZ0UsR0FBRzt3QkFDVEMsU0FBUzs0QkFDUDFDLEdBQUc7Z0NBQUMsQ0FBQztnQ0FBSTtnQ0FBSSxDQUFDOzZCQUFHOzRCQUNqQjJDLFFBQVE7Z0NBQUM7Z0NBQUc7Z0NBQUs7NkJBQUk7NEJBQ3JCQyxPQUFPO2dDQUFDO2dDQUFHO2dDQUFLOzZCQUFFO3dCQUNwQjt3QkFDQWIsWUFBWTs0QkFBRUcsVUFBVTs0QkFBSVcsUUFBUUM7NEJBQVVYLE1BQU07d0JBQVk7d0JBQ2hFRyxXQUFVO3dCQUNWQyxPQUFPOzRCQUNMQyxZQUFZOzRCQUNaTyxnQkFBZ0I7NEJBQ2hCQyxzQkFBc0I7NEJBQ3RCQyxRQUFROzRCQUNSQyxXQUFXO3dCQUNiOzs7Ozs7a0NBR0YsOERBQUN6RSxpREFBTUEsQ0FBQ2dFLEdBQUc7d0JBQ1RDLFNBQVM7NEJBQ1AxQyxHQUFHO2dDQUFDO2dDQUFJLENBQUM7Z0NBQUk7NkJBQUc7NEJBQ2hCMkMsUUFBUTtnQ0FBQztnQ0FBSztnQ0FBSzs2QkFBRTs0QkFDckJDLE9BQU87Z0NBQUM7Z0NBQUs7Z0NBQUc7NkJBQUk7d0JBQ3RCO3dCQUNBYixZQUFZOzRCQUFFRyxVQUFVOzRCQUFJVyxRQUFRQzs0QkFBVVgsTUFBTTt3QkFBWTt3QkFDaEVHLFdBQVU7d0JBQ1ZDLE9BQU87NEJBQ0xDLFlBQVk7NEJBQ1pPLGdCQUFnQjs0QkFDaEJDLHNCQUFzQjs0QkFDdEJDLFFBQVE7NEJBQ1JDLFdBQVc7d0JBQ2I7Ozs7OztrQ0FHRiw4REFBQ3pFLGlEQUFNQSxDQUFDZ0UsR0FBRzt3QkFDVEMsU0FBUzs0QkFDUDFDLEdBQUc7Z0NBQUMsQ0FBQztnQ0FBSTtnQ0FBSSxDQUFDOzZCQUFHOzRCQUNqQkQsR0FBRztnQ0FBQyxDQUFDO2dDQUFJO2dDQUFJLENBQUM7NkJBQUc7NEJBQ2pCNEMsUUFBUTtnQ0FBQztnQ0FBRztnQ0FBSTs2QkFBSTt3QkFDdEI7d0JBQ0FaLFlBQVk7NEJBQUVHLFVBQVU7NEJBQUlXLFFBQVFDOzRCQUFVWCxNQUFNO3dCQUFZO3dCQUNoRUcsV0FBVTt3QkFDVkMsT0FBTzs0QkFDTEMsWUFBWTs0QkFDWk8sZ0JBQWdCOzRCQUNoQkMsc0JBQXNCOzRCQUN0QkMsUUFBUTs0QkFDUkMsV0FBVzt3QkFDYjs7Ozs7O29CQUlEOzJCQUFJQyxNQUFNO3FCQUFHLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUM3RSxpREFBTUEsQ0FBQ2dFLEdBQUc7NEJBRVRDLFNBQVM7Z0NBQ1AxQyxHQUFHO29DQUFDLENBQUM7b0NBQUk7b0NBQUksQ0FBQztpQ0FBRztnQ0FDakJELEdBQUc7b0NBQUMsQ0FBQztvQ0FBSTtvQ0FBSSxDQUFDO2lDQUFHO2dDQUNqQjhCLFNBQVM7b0NBQUM7b0NBQUs7b0NBQUs7aUNBQUk7Z0NBQ3hCZSxPQUFPO29DQUFDO29DQUFHO29DQUFLO2lDQUFFOzRCQUNwQjs0QkFDQWIsWUFBWTtnQ0FDVkcsVUFBVSxJQUFJb0IsSUFBSTtnQ0FDbEJULFFBQVFDO2dDQUNSWCxNQUFNO2dDQUNOb0IsT0FBT0QsSUFBSTs0QkFDYjs0QkFDQWhCLFdBQVU7NEJBQ1ZDLE9BQU87Z0NBQ0xpQixNQUFNLEdBQWlCLE9BQWQsS0FBTUYsSUFBSSxJQUFJO2dDQUN2QkcsS0FBSyxHQUFnQixPQUFiLEtBQU1ILElBQUksR0FBRztnQ0FDckJJLFFBQVE7NEJBQ1Y7MkJBbEJLSjs7Ozs7Ozs7Ozs7MEJBdUJYLDhEQUFDYjtnQkFBSUgsV0FBVTswQkFDYiw0RUFBQzdELGlEQUFNQSxDQUFDZ0UsR0FBRztvQkFDVGtCLFVBQVVoQztvQkFDVmlDLFNBQVE7b0JBQ1JsQixTQUFTL0IsU0FBUyxZQUFZO29CQUM5QjJCLFdBQVU7O3NDQUdWLDhEQUFDN0QsaURBQU1BLENBQUNnRSxHQUFHOzRCQUNUa0IsVUFBVTFCOzRCQUNWSyxXQUFVO3NDQUVWLDRFQUFDN0QsaURBQU1BLENBQUNnRSxHQUFHO2dDQUNUSCxXQUFVO2dDQUNWdUIsWUFBWTtvQ0FBRWpCLE9BQU87b0NBQUtELFFBQVE7Z0NBQUU7Z0NBQ3BDWixZQUFZO29DQUFFRyxVQUFVO2dDQUFJO2dDQUM1QkssT0FBTztvQ0FDTEMsWUFBWTtvQ0FDWk8sZ0JBQWdCO29DQUNoQkMsc0JBQXNCO29DQUN0QkMsUUFBUTtvQ0FDUkMsV0FBVztnQ0FDYjs7a0RBRUEsOERBQUNuRSx3SUFBWUE7d0NBQUN1RCxXQUFVOzs7Ozs7a0RBQ3hCLDhEQUFDckQsd0lBQVlBO3dDQUFDcUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzVCLDhEQUFDN0QsaURBQU1BLENBQUNnRSxHQUFHOzRCQUNUa0IsVUFBVTFCOzRCQUNWSyxXQUFVOzs4Q0FFViw4REFBQzdELGlEQUFNQSxDQUFDcUYsRUFBRTtvQ0FDUnhCLFdBQVU7b0NBQ1ZzQixTQUFTO3dDQUFFL0IsU0FBUzt3Q0FBRzdCLEdBQUc7b0NBQUc7b0NBQzdCMEMsU0FBUzt3Q0FBRWIsU0FBUzt3Q0FBRzdCLEdBQUc7b0NBQUU7b0NBQzVCK0IsWUFBWTt3Q0FBRUcsVUFBVTtvQ0FBSTs4Q0FFM0IzQyxFQUFFOzs7Ozs7OENBSUwsOERBQUNrRDtvQ0FDQ0gsV0FBVTtvQ0FDVkMsT0FBTzt3Q0FDTEMsWUFBWTt3Q0FDWk8sZ0JBQWdCO3dDQUNoQkMsc0JBQXNCO3dDQUN0QkMsUUFBUTt3Q0FDUkMsV0FBVztvQ0FDYjs7Ozs7Ozs7Ozs7O3NDQUtKLDhEQUFDekUsaURBQU1BLENBQUNzRixDQUFDOzRCQUNQSixVQUFVMUI7NEJBQ1ZLLFdBQVU7NEJBQ1ZzQixTQUFTO2dDQUFFL0IsU0FBUztnQ0FBRzdCLEdBQUc7NEJBQUc7NEJBQzdCMEMsU0FBUztnQ0FBRWIsU0FBUztnQ0FBRzdCLEdBQUc7NEJBQUU7NEJBQzVCK0IsWUFBWTtnQ0FBRUcsVUFBVTtnQ0FBS3FCLE9BQU87NEJBQUk7c0NBRXZDaEUsRUFBRTs7Ozs7O3NDQUlMLDhEQUFDZCxpREFBTUEsQ0FBQ2dFLEdBQUc7NEJBQ1RrQixVQUFVMUI7NEJBQ1ZLLFdBQVU7OzhDQUVWLDhEQUFDMEI7b0NBQUs1QyxVQUFVTCxhQUFhSztvQ0FBV2tCLFdBQVU7O3NEQUNoRCw4REFBQ0c7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDMkI7b0RBQ0UsR0FBR25ELFNBQVMsUUFBUTtvREFDckJvRCxNQUFLO29EQUNMQyxhQUFhNUUsRUFBRTtvREFDZitDLFdBQVcsNEtBRVYsT0FEQzVDLFFBQVEsZUFBZTtvREFFekIwRSxVQUFVekU7Ozs7OztnREFFWHVCLE9BQU85QixLQUFLLGtCQUNYLDhEQUFDMkU7b0RBQUV6QixXQUFVOzhEQUNWL0MsRUFBRTs7Ozs7Ozs7Ozs7O3NEQUtULDhEQUFDOEU7NENBQ0NILE1BQUs7NENBQ0xFLFVBQVV6RTs0Q0FDVjJDLFdBQVU7O2dEQUVUM0MsNkJBQ0MsOERBQUM4QztvREFBSUgsV0FBVTs7Ozs7eUVBRWYsOERBQUN0RCw2SUFBaUJBO29EQUFDc0QsV0FBVTs7Ozs7O2dEQUU5QjNDLGVBQWVKLEVBQUUsMkJBQTJCQSxFQUFFOzs7Ozs7Ozs7Ozs7OzhDQUtuRCw4REFBQ3dFO29DQUFFekIsV0FBVTs4Q0FDVjVDLFFBQ0csOEVBQ0E7Ozs7Ozs7Ozs7OztzQ0FNUiw4REFBQ2pCLGlEQUFNQSxDQUFDZ0UsR0FBRzs0QkFDVGtCLFVBQVUxQjs0QkFDVkssV0FBVTs7OENBRVYsOERBQUNHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7c0RBQ2IsNEVBQUNnQztnREFBS2hDLFdBQVU7MERBQStCOzs7Ozs7Ozs7OztzREFFakQsOERBQUNpQzs0Q0FBR2pDLFdBQVU7c0RBQ1g1QyxRQUFRLGtCQUFrQjs7Ozs7O3NEQUU3Qiw4REFBQ3FFOzRDQUFFekIsV0FBVTtzREFDVjVDLFFBQ0cseUNBQ0E7Ozs7Ozs7Ozs7Ozs4Q0FLUiw4REFBQytDO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7c0RBQ2IsNEVBQUNnQztnREFBS2hDLFdBQVU7MERBQStCOzs7Ozs7Ozs7OztzREFFakQsOERBQUNpQzs0Q0FBR2pDLFdBQVU7c0RBQ1g1QyxRQUFRLGNBQWM7Ozs7OztzREFFekIsOERBQUNxRTs0Q0FBRXpCLFdBQVU7c0RBQ1Y1QyxRQUNHLGtDQUNBOzs7Ozs7Ozs7Ozs7OENBS1IsOERBQUMrQztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFJSCxXQUFVO3NEQUNiLDRFQUFDZ0M7Z0RBQUtoQyxXQUFVOzBEQUErQjs7Ozs7Ozs7Ozs7c0RBRWpELDhEQUFDaUM7NENBQUdqQyxXQUFVO3NEQUNYNUMsUUFBUSxnQkFBZ0I7Ozs7OztzREFFM0IsOERBQUNxRTs0Q0FBRXpCLFdBQVU7c0RBQ1Y1QyxRQUNHLHFDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNwQjtHQWpWd0JKOztRQUNSZix3REFBY0E7UUFDYkMsa0RBQVNBO1FBb0JGRSxrRUFBU0E7UUFVM0JDLG9EQUFPQTs7O0tBaENXVyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9zZWN0aW9ucy9OZXdzbGV0dGVyLnRzeD9iMGE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IHVzZUluVmlldyB9IGZyb20gJ3JlYWN0LWludGVyc2VjdGlvbi1vYnNlcnZlcic7XG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSAncmVhY3QtaG9vay1mb3JtJztcbmltcG9ydCB7IHpvZFJlc29sdmVyIH0gZnJvbSAnQGhvb2tmb3JtL3Jlc29sdmVycy96b2QnO1xuaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCc7XG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCB7IEVudmVsb3BlSWNvbiwgUGFwZXJBaXJwbGFuZUljb24sIFNwYXJrbGVzSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbi8vIFZhbGlkYXRpb24gc2NoZW1hXG5jb25zdCBuZXdzbGV0dGVyU2NoZW1hID0gei5vYmplY3Qoe1xuICBlbWFpbDogei5zdHJpbmcoKS5lbWFpbCgnSW52YWxpZCBlbWFpbCBhZGRyZXNzJyksXG59KTtcblxudHlwZSBOZXdzbGV0dGVyRm9ybURhdGEgPSB6LmluZmVyPHR5cGVvZiBuZXdzbGV0dGVyU2NoZW1hPjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmV3c2xldHRlcigpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbignbGFuZGluZycpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyBsb2NhbGUgfSA9IHJvdXRlcjtcbiAgY29uc3QgaXNSVEwgPSBsb2NhbGUgPT09ICdhcic7XG5cbiAgY29uc3QgW2lzU3VibWl0dGluZywgc2V0SXNTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21vdXNlUG9zaXRpb24sIHNldE1vdXNlUG9zaXRpb25dID0gdXNlU3RhdGUoeyB4OiAwLCB5OiAwIH0pO1xuXG4gIC8vIFRyYWNrIG1vdXNlIG1vdmVtZW50IGZvciBpbnRlcmFjdGl2ZSBlZmZlY3RzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gKGU6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAgIHNldE1vdXNlUG9zaXRpb24oe1xuICAgICAgICB4OiAoZS5jbGllbnRYIC8gd2luZG93LmlubmVyV2lkdGgpICogMTAwLFxuICAgICAgICB5OiAoZS5jbGllbnRZIC8gd2luZG93LmlubmVySGVpZ2h0KSAqIDEwMCxcbiAgICAgIH0pO1xuICAgIH07XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBbcmVmLCBpblZpZXddID0gdXNlSW5WaWV3KHtcbiAgICB0cmlnZ2VyT25jZTogdHJ1ZSxcbiAgICB0aHJlc2hvbGQ6IDAuMSxcbiAgfSk7XG5cbiAgY29uc3Qge1xuICAgIHJlZ2lzdGVyLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICByZXNldCxcbiAgICBmb3JtU3RhdGU6IHsgZXJyb3JzIH0sXG4gIH0gPSB1c2VGb3JtPE5ld3NsZXR0ZXJGb3JtRGF0YT4oe1xuICAgIHJlc29sdmVyOiB6b2RSZXNvbHZlcihuZXdzbGV0dGVyU2NoZW1hKSxcbiAgfSk7XG5cbiAgY29uc3Qgb25TdWJtaXQgPSBhc3luYyAoX2RhdGE6IE5ld3NsZXR0ZXJGb3JtRGF0YSkgPT4ge1xuICAgIHNldElzU3VibWl0dGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8gU2ltdWxhdGUgQVBJIGNhbGxcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG4gICAgICBcbiAgICAgIC8vIEluIGEgcmVhbCBhcHAsIHlvdSB3b3VsZCBzZW5kIHRoZSBlbWFpbCB0byB5b3VyIG5ld3NsZXR0ZXIgc2VydmljZVxuICAgICAgLy8gVE9ETzogU2VuZCBlbWFpbCB0byBuZXdzbGV0dGVyIHNlcnZpY2VcbiAgICAgIFxuICAgICAgdG9hc3Quc3VjY2Vzcyh0KCduZXdzbGV0dGVyLnN1Y2Nlc3MnKSk7XG4gICAgICByZXNldCgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5lcnJvcih0KCduZXdzbGV0dGVyLmVycm9yJykpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjb250YWluZXJWYXJpYW50cyA9IHtcbiAgICBoaWRkZW46IHsgb3BhY2l0eTogMCB9LFxuICAgIHZpc2libGU6IHtcbiAgICAgIG9wYWNpdHk6IDEsXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIHN0YWdnZXJDaGlsZHJlbjogMC4xLFxuICAgICAgfSxcbiAgICB9LFxuICB9O1xuXG4gIGNvbnN0IGl0ZW1WYXJpYW50cyA9IHtcbiAgICBoaWRkZW46IHsgb3BhY2l0eTogMCwgeTogMzAgfSxcbiAgICB2aXNpYmxlOiB7XG4gICAgICBvcGFjaXR5OiAxLFxuICAgICAgeTogMCxcbiAgICAgIHRyYW5zaXRpb246IHtcbiAgICAgICAgZHVyYXRpb246IDAuNixcbiAgICAgICAgZWFzZTogJ2Vhc2VPdXQnLFxuICAgICAgfSxcbiAgICB9LFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb25cbiAgICAgIGlkPVwibmV3c2xldHRlclwiXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT1cInNlY3Rpb24tcGFkZGluZyByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgc3R5bGU9e3tcbiAgICAgICAgYmFja2dyb3VuZDogYFxuICAgICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgJHttb3VzZVBvc2l0aW9uLnh9JSAke21vdXNlUG9zaXRpb24ueX0lLCByZ2JhKDIwNiwgMTcsIDM4LCAwLjE1KSAwJSwgdHJhbnNwYXJlbnQgNjAlKSxcbiAgICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0ICR7MTAwIC0gbW91c2VQb3NpdGlvbi54fSUgJHsxMDAgLSBtb3VzZVBvc2l0aW9uLnl9JSwgcmdiYSgwLCAxMjIsIDYxLCAwLjEyKSAwJSwgdHJhbnNwYXJlbnQgNjAlKSxcbiAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDIwNjE3IDAlLCAjMGYxNzJhIDI1JSwgIzFlMjkzYiA1MCUsICMzMzQxNTUgNzUlLCAjNDc1NTY5IDEwMCUpXG4gICAgICAgIGBcbiAgICAgIH19XG4gICAgPlxuICAgICAgey8qIEVuaGFuY2VkIEJhY2tncm91bmQgd2l0aCBHbGFzcyBPcmJzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LWhpZGRlbiBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgIHsvKiBMYXJnZSBHbGFzcyBPcmJzIHdpdGggU3lyaWFuIENvbG9ycyAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICB5OiBbLTMwLCAzMCwgLTMwXSxcbiAgICAgICAgICAgIHJvdGF0ZTogWzAsIDE4MCwgMzYwXSxcbiAgICAgICAgICAgIHNjYWxlOiBbMSwgMS4xLCAxXVxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMjAsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6ICdlYXNlSW5PdXQnIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMTAgdy0zMiBoLTMyIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTIwXCJcbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjA2LCAxNywgMzgsIDAuMSknLFxuICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDIwcHgpJyxcbiAgICAgICAgICAgIFdlYmtpdEJhY2tkcm9wRmlsdGVyOiAnYmx1cigyMHB4KScsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyMDYsIDE3LCAzOCwgMC4yKScsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAzMnB4IHJnYmEoMjA2LCAxNywgMzgsIDAuMSknXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cblxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHk6IFs0MCwgLTQwLCA0MF0sXG4gICAgICAgICAgICByb3RhdGU6IFszNjAsIDE4MCwgMF0sXG4gICAgICAgICAgICBzY2FsZTogWzEuMSwgMSwgMS4xXVxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMjUsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6ICdlYXNlSW5PdXQnIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQwIHJpZ2h0LTIwIHctMjQgaC0yNCByb3VuZGVkLWZ1bGwgb3BhY2l0eS0xNVwiXG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsIDEyMiwgNjEsIDAuMSknLFxuICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDE1cHgpJyxcbiAgICAgICAgICAgIFdlYmtpdEJhY2tkcm9wRmlsdGVyOiAnYmx1cigxNXB4KScsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgwLCAxMjIsIDYxLCAwLjIpJyxcbiAgICAgICAgICAgIGJveFNoYWRvdzogJzAgOHB4IDMycHggcmdiYSgwLCAxMjIsIDYxLCAwLjEpJ1xuICAgICAgICAgIH19XG4gICAgICAgIC8+XG5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICB5OiBbLTI1LCAyNSwgLTI1XSxcbiAgICAgICAgICAgIHg6IFstMTAsIDEwLCAtMTBdLFxuICAgICAgICAgICAgcm90YXRlOiBbMCwgOTAsIDE4MF1cbiAgICAgICAgICB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDE4LCByZXBlYXQ6IEluZmluaXR5LCBlYXNlOiAnZWFzZUluT3V0JyB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS00MCBsZWZ0LTIwIHctNDAgaC00MCByb3VuZGVkLWZ1bGwgb3BhY2l0eS0xMFwiXG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDEyNCwgNTgsIDIzNywgMC4xKScsXG4gICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMjVweCknLFxuICAgICAgICAgICAgV2Via2l0QmFja2Ryb3BGaWx0ZXI6ICdibHVyKDI1cHgpJyxcbiAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDEyNCwgNTgsIDIzNywgMC4yKScsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAzMnB4IHJnYmEoMTI0LCA1OCwgMjM3LCAwLjEpJ1xuICAgICAgICAgIH19XG4gICAgICAgIC8+XG5cbiAgICAgICAgey8qIEZsb2F0aW5nIFBhcnRpY2xlcyAqL31cbiAgICAgICAge1suLi5BcnJheSg4KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgICAgeTogWy0yMCwgMjAsIC0yMF0sXG4gICAgICAgICAgICAgIHg6IFstMTAsIDEwLCAtMTBdLFxuICAgICAgICAgICAgICBvcGFjaXR5OiBbMC4xLCAwLjMsIDAuMV0sXG4gICAgICAgICAgICAgIHNjYWxlOiBbMSwgMS4yLCAxXVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgICAgZHVyYXRpb246IDggKyBpICogMixcbiAgICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgICAgZWFzZTogJ2Vhc2VJbk91dCcsXG4gICAgICAgICAgICAgIGRlbGF5OiBpICogMC41XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdy0yIGgtMiBiZy13aGl0ZSByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgbGVmdDogYCR7MTAgKyAoaSAqIDEyKX0lYCxcbiAgICAgICAgICAgICAgdG9wOiBgJHsyMCArIChpICogOCl9JWAsXG4gICAgICAgICAgICAgIGZpbHRlcjogJ2JsdXIoMXB4KSdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBjb250YWluZXItcGFkZGluZyByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgdmFyaWFudHM9e2NvbnRhaW5lclZhcmlhbnRzfVxuICAgICAgICAgIGluaXRpYWw9XCJoaWRkZW5cIlxuICAgICAgICAgIGFuaW1hdGU9e2luVmlldyA/ICd2aXNpYmxlJyA6ICdoaWRkZW4nfVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHRleHQtY2VudGVyXCJcbiAgICAgICAgPlxuICAgICAgICAgIHsvKiBFbmhhbmNlZCBJY29uIHdpdGggR2xhc3MgRWZmZWN0ICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICB2YXJpYW50cz17aXRlbVZhcmlhbnRzfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItMTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMjQgaC0yNCBteC1hdXRvIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByZWxhdGl2ZSB6LTEwXCJcbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xLCByb3RhdGU6IDUgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCkgMTAwJSknLFxuICAgICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigyMHB4KScsXG4gICAgICAgICAgICAgICAgV2Via2l0QmFja2Ryb3BGaWx0ZXI6ICdibHVyKDIwcHgpJyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlIGFic29sdXRlIC10b3AtMiAtcmlnaHQtMiBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogRW5oYW5jZWQgVGl0bGUgd2l0aCBHbGFzcyBDb250YWluZXIgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYi04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhlYWRpbmctbGcgdGV4dC13aGl0ZSBtYi00IHJlbGF0aXZlIHotMTAgcHgtOCBweS00IHRleHQtYXJhYmljLXByZW1pdW1cIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3QoJ25ld3NsZXR0ZXIudGl0bGUnKX1cbiAgICAgICAgICAgIDwvbW90aW9uLmgyPlxuXG4gICAgICAgICAgICB7LyogR2xhc3MgYmFja2Ryb3AgZm9yIHRpdGxlICovfVxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtMnhsIG9wYWNpdHktMzBcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDgpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDMpIDEwMCUpJyxcbiAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMzBweCknLFxuICAgICAgICAgICAgICAgIFdlYmtpdEJhY2tkcm9wRmlsdGVyOiAnYmx1cigzMHB4KScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSknLFxuICAgICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgMjBweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xKSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgey8qIEVuaGFuY2VkIFN1YnRpdGxlICovfVxuICAgICAgICAgIDxtb3Rpb24ucFxuICAgICAgICAgICAgdmFyaWFudHM9e2l0ZW1WYXJpYW50c31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC13aGl0ZS85MCBtYi0xNiBtYXgtdy0zeGwgbXgtYXV0byB0ZXh0LWNlbnRlciBsZWFkaW5nLXJlbGF4ZWQgdGV4dC1hcmFiaWMgcHgtNlwiXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMyB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHt0KCduZXdzbGV0dGVyLnN1YnRpdGxlJyl9XG4gICAgICAgICAgPC9tb3Rpb24ucD5cblxuICAgICAgICAgIHsvKiBOZXdzbGV0dGVyIEZvcm0gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0KG9uU3VibWl0KX0gY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignZW1haWwnKX1cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dCgnbmV3c2xldHRlci5wbGFjZWhvbGRlcicpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTYgcHktNCByb3VuZGVkLWxnIGJnLXdoaXRlLzk1IGJhY2tkcm9wLWJsdXItc20gdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTUwMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgIGlzUlRMID8gJ3RleHQtcmlnaHQnIDogJ3RleHQtbGVmdCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZW1haWwgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS85MCB0ZXh0LXNtIG10LTIgdGV4dC1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICB7dCgnY29tbW9uLnZhbGlkYXRpb24uZW1haWwnKX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUgdGV4dC1wcmltYXJ5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIGZvbnQtbWVkaXVtIHB4LTYgcHktNCByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMC41IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOnRyYW5zZm9ybS1ub25lIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNSBoLTUgYm9yZGVyLTIgYm9yZGVyLXByaW1hcnktNjAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8UGFwZXJBaXJwbGFuZUljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gdCgnY29tbW9uLnN0YXR1cy5sb2FkaW5nJykgOiB0KCduZXdzbGV0dGVyLnN1Ym1pdCcpfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZm9ybT5cblxuICAgICAgICAgICAgey8qIEFkZGl0aW9uYWwgSW5mbyAqL31cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNzAgdGV4dC1zbSBtdC02XCI+XG4gICAgICAgICAgICAgIHtpc1JUTCBcbiAgICAgICAgICAgICAgICA/ICfZhNmGINmG2LTYp9ix2YMg2KjYsdmK2K/ZgyDYp9mE2KXZhNmD2KrYsdmI2YbZiiDZhdi5INij2Yog2LfYsdmBINir2KfZhNirLiDZitmF2YPZhtmDINil2YTYutin2KEg2KfZhNin2LTYqtix2KfZgyDZgdmKINij2Yog2YjZgtiqLidcbiAgICAgICAgICAgICAgICA6ICdXZSB3b25cXCd0IHNoYXJlIHlvdXIgZW1haWwgd2l0aCBhbnlvbmUgZWxzZS4gWW91IGNhbiB1bnN1YnNjcmliZSBhdCBhbnkgdGltZS4nXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogQmVuZWZpdHMgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC04IG10LTE2XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIG14LWF1dG8gbWItNCBiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+8J+Tpzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgIHtpc1JUTCA/ICfYo9iu2KjYp9ixINij2LPYqNmI2LnZitipJyA6ICdXZWVrbHkgVXBkYXRlcyd9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvODAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIHtpc1JUTCBcbiAgICAgICAgICAgICAgICAgID8gJ9in2K3YtdmEINi52YTZiSDYotiu2LEg2KfZhNij2K7YqNin2LEg2YjYp9mE2YHYsdi1INmD2YQg2KPYs9io2YjYuSdcbiAgICAgICAgICAgICAgICAgIDogJ0dldCB0aGUgbGF0ZXN0IG5ld3MgYW5kIG9wcG9ydHVuaXRpZXMgZXZlcnkgd2VlaydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIG14LWF1dG8gbWItNCBiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+8J+Orzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgIHtpc1JUTCA/ICfZgdix2LUg2K3Ytdix2YrYqScgOiAnRXhjbHVzaXZlIE9wcG9ydHVuaXRpZXMnfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzgwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7aXNSVEwgXG4gICAgICAgICAgICAgICAgICA/ICfZg9mGINij2YjZhCDZhdmGINmK2LnZhNmFINio2KfZhNmB2LHYtSDYp9mE2KzYr9mK2K/YqSdcbiAgICAgICAgICAgICAgICAgIDogJ0JlIHRoZSBmaXJzdCB0byBrbm93IGFib3V0IG5ldyBvcHBvcnR1bml0aWVzJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgbXgtYXV0byBtYi00IGJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj7wn5KhPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAge2lzUlRMID8gJ9mG2LXYp9im2K0g2YXZgdmK2K/YqScgOiAnSGVscGZ1bCBUaXBzJ31cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge2lzUlRMIFxuICAgICAgICAgICAgICAgICAgPyAn2YbYtdin2KbYrSDZhNiq2LfZiNmK2LEg2YXZh9in2LHYp9iq2YMg2YjYstmK2KfYr9ipINiv2K7ZhNmDJ1xuICAgICAgICAgICAgICAgICAgOiAnVGlwcyB0byBkZXZlbG9wIHlvdXIgc2tpbGxzIGFuZCBpbmNyZWFzZSB5b3VyIGluY29tZSdcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlVHJhbnNsYXRpb24iLCJ1c2VSb3V0ZXIiLCJtb3Rpb24iLCJ1c2VJblZpZXciLCJ1c2VGb3JtIiwiem9kUmVzb2x2ZXIiLCJ6IiwidG9hc3QiLCJFbnZlbG9wZUljb24iLCJQYXBlckFpcnBsYW5lSWNvbiIsIlNwYXJrbGVzSWNvbiIsIm5ld3NsZXR0ZXJTY2hlbWEiLCJvYmplY3QiLCJlbWFpbCIsInN0cmluZyIsIk5ld3NsZXR0ZXIiLCJ0Iiwicm91dGVyIiwibG9jYWxlIiwiaXNSVEwiLCJpc1N1Ym1pdHRpbmciLCJzZXRJc1N1Ym1pdHRpbmciLCJtb3VzZVBvc2l0aW9uIiwic2V0TW91c2VQb3NpdGlvbiIsIngiLCJ5IiwiaGFuZGxlTW91c2VNb3ZlIiwiZSIsImNsaWVudFgiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiY2xpZW50WSIsImlubmVySGVpZ2h0IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJyZWYiLCJpblZpZXciLCJ0cmlnZ2VyT25jZSIsInRocmVzaG9sZCIsInJlZ2lzdGVyIiwiaGFuZGxlU3VibWl0IiwicmVzZXQiLCJmb3JtU3RhdGUiLCJlcnJvcnMiLCJyZXNvbHZlciIsIm9uU3VibWl0IiwiX2RhdGEiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJzdWNjZXNzIiwiZXJyb3IiLCJjb250YWluZXJWYXJpYW50cyIsImhpZGRlbiIsIm9wYWNpdHkiLCJ2aXNpYmxlIiwidHJhbnNpdGlvbiIsInN0YWdnZXJDaGlsZHJlbiIsIml0ZW1WYXJpYW50cyIsImR1cmF0aW9uIiwiZWFzZSIsInNlY3Rpb24iLCJpZCIsImNsYXNzTmFtZSIsInN0eWxlIiwiYmFja2dyb3VuZCIsImRpdiIsImFuaW1hdGUiLCJyb3RhdGUiLCJzY2FsZSIsInJlcGVhdCIsIkluZmluaXR5IiwiYmFja2Ryb3BGaWx0ZXIiLCJXZWJraXRCYWNrZHJvcEZpbHRlciIsImJvcmRlciIsImJveFNoYWRvdyIsIkFycmF5IiwibWFwIiwiXyIsImkiLCJkZWxheSIsImxlZnQiLCJ0b3AiLCJmaWx0ZXIiLCJ2YXJpYW50cyIsImluaXRpYWwiLCJ3aGlsZUhvdmVyIiwiaDIiLCJwIiwiZm9ybSIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJidXR0b24iLCJzcGFuIiwiaDMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/sections/Newsletter.tsx\n"));

/***/ })

});