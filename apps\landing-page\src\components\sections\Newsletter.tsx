import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { EnvelopeIcon, PaperAirplaneIcon, SparklesIcon } from '@heroicons/react/24/outline';

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email('Invalid email address'),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

export default function Newsletter() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
  });

  const onSubmit = async (_data: NewsletterFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would send the email to your newsletter service
      // TODO: Send email to newsletter service
      
      toast.success(t('newsletter.success'));
      reset();
    } catch (error) {
      toast.error(t('newsletter.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="newsletter"
      ref={ref}
      className="section-padding relative overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(206, 17, 38, 0.15) 0%, transparent 60%),
          radial-gradient(circle at ${100 - mousePosition.x}% ${100 - mousePosition.y}%, rgba(0, 122, 61, 0.12) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large Glass Orbs with Syrian Colors */}
        <motion.div
          animate={{
            y: [-30, 30, -30],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 left-10 w-32 h-32 rounded-full opacity-20"
          style={{
            background: 'rgba(206, 17, 38, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: '1px solid rgba(206, 17, 38, 0.2)',
            boxShadow: '0 8px 32px rgba(206, 17, 38, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [40, -40, 40],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 25, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-40 right-20 w-24 h-24 rounded-full opacity-15"
          style={{
            background: 'rgba(0, 122, 61, 0.1)',
            backdropFilter: 'blur(15px)',
            WebkitBackdropFilter: 'blur(15px)',
            border: '1px solid rgba(0, 122, 61, 0.2)',
            boxShadow: '0 8px 32px rgba(0, 122, 61, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [-25, 25, -25],
            x: [-10, 10, -10],
            rotate: [0, 90, 180]
          }}
          transition={{ duration: 18, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 left-20 w-40 h-40 rounded-full opacity-10"
          style={{
            background: 'rgba(124, 58, 237, 0.1)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: '1px solid rgba(124, 58, 237, 0.2)',
            boxShadow: '0 8px 32px rgba(124, 58, 237, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.5
            }}
            className="absolute w-2 h-2 bg-white rounded-full"
            style={{
              left: `${10 + (i * 12)}%`,
              top: `${20 + (i * 8)}%`,
              filter: 'blur(1px)'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="max-w-4xl mx-auto text-center"
        >
          {/* Enhanced Icon with Glass Effect */}
          <motion.div
            variants={itemVariants}
            className="relative mb-12"
          >
            <motion.div
              className="w-24 h-24 mx-auto rounded-full flex items-center justify-center relative z-10"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.3 }}
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
              }}
            >
              <EnvelopeIcon className="w-12 h-12 text-white" />
              <SparklesIcon className="w-6 h-6 text-white absolute -top-2 -right-2 animate-pulse" />
            </motion.div>
          </motion.div>

          {/* Enhanced Title with Glass Container */}
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >
            <motion.h2
              className="heading-lg text-white mb-4 relative z-10 px-8 py-4 text-arabic-premium"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {t('newsletter.title')}
            </motion.h2>

            {/* Glass backdrop for title */}
            <div
              className="absolute inset-0 rounded-2xl opacity-30"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
              }}
            />
          </motion.div>

          {/* Enhanced Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 mb-16 max-w-3xl mx-auto text-center leading-relaxed text-arabic px-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {t('newsletter.subtitle')}
          </motion.p>

          {/* Enhanced Newsletter Form with Glass Effect */}
          <motion.div
            variants={itemVariants}
            className="max-w-lg mx-auto relative"
          >
            <div
              className="relative p-8 rounded-3xl"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="relative">
                  <input
                    {...register('email')}
                    type="email"
                    placeholder={t('newsletter.placeholder')}
                    className={`w-full px-6 py-4 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic ${
                      isRTL ? 'text-right' : 'text-left'
                    }`}
                    style={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.3)'
                    }}
                    disabled={isSubmitting}
                  />
                  {errors.email && (
                    <p className="text-white/90 text-sm mt-2 text-start text-arabic">
                      {t('common.validation.email')}
                    </p>
                  )}
                </div>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full font-bold px-8 py-4 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 group text-arabic-premium"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)',
                    color: '#1e293b',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  {isSubmitting ? (
                    <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <PaperAirplaneIcon className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                  )}
                  {isSubmitting ? t('common.status.loading') : t('newsletter.submit')}
                  {!isSubmitting && <SparklesIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />}
                </motion.button>
              </form>
            </div>

            {/* Additional Info */}
            <p className="text-white/70 text-sm mt-6 text-center text-arabic">
              {isRTL
                ? 'لن نشارك بريدك الإلكتروني مع أي طرف ثالث. يمكنك إلغاء الاشتراك في أي وقت.'
                : 'We won\'t share your email with anyone else. You can unsubscribe at any time.'
              }
            </p>
          </div>
          </motion.div>

          {/* Enhanced Benefits with Glass Cards */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-20"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            {[
              {
                icon: '📧',
                title: isRTL ? 'أخبار أسبوعية' : 'Weekly Updates',
                description: isRTL
                  ? 'احصل على آخر الأخبار والفرص كل أسبوع'
                  : 'Get the latest news and opportunities every week',
                color: 'rgba(206, 17, 38, 0.1)'
              },
              {
                icon: '🎯',
                title: isRTL ? 'فرص حصرية' : 'Exclusive Opportunities',
                description: isRTL
                  ? 'كن أول من يعلم بالفرص الجديدة'
                  : 'Be the first to know about new opportunities',
                color: 'rgba(0, 122, 61, 0.1)'
              },
              {
                icon: '💡',
                title: isRTL ? 'نصائح مفيدة' : 'Helpful Tips',
                description: isRTL
                  ? 'نصائح لتطوير مهاراتك وزيادة دخلك'
                  : 'Tips to develop your skills and increase your income',
                color: 'rgba(124, 58, 237, 0.1)'
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.8 + (index * 0.1),
                  type: 'spring',
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.05,
                  y: -8,
                  transition: { duration: 0.2 }
                }}
                className="text-center p-8 rounded-2xl group cursor-pointer"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                }}
              >
                <motion.div
                  className="flex items-center justify-center mb-6"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div
                    className="w-16 h-16 rounded-2xl flex items-center justify-center relative overflow-hidden"
                    style={{
                      background: benefit.color,
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <span className="text-white font-bold text-2xl group-hover:scale-110 transition-transform duration-300">
                      {benefit.icon}
                    </span>
                  </div>
                </motion.div>

                <h3 className="font-bold text-white mb-4 text-lg text-arabic-premium group-hover:scale-105 transition-transform duration-300">
                  {benefit.title}
                </h3>
                <p className="text-white/80 text-sm leading-relaxed text-arabic">
                  {benefit.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
