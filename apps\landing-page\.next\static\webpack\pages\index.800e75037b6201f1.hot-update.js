"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/Newsletter.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Newsletter.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Newsletter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"../../node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon,SparklesIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Validation schema\nconst newsletterSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email(\"Invalid email address\")\n});\nfunction Newsletter() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(newsletterSchema)\n    });\n    const onSubmit = async (_data)=>{\n        setIsSubmitting(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // In a real app, you would send the email to your newsletter service\n            // TODO: Send email to newsletter service\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(t(\"newsletter.success\"));\n            reset();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(t(\"newsletter.error\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"newsletter\",\n        ref: ref,\n        className: \"section-padding relative overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(206, 17, 38, 0.15) 0%, transparent 60%),\\n          radial-gradient(circle at \").concat(100 - mousePosition.x, \"% \").concat(100 - mousePosition.y, \"%, rgba(0, 122, 61, 0.12) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -30,\n                                30,\n                                -30\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 w-32 h-32 rounded-full opacity-20\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                40,\n                                -40,\n                                40\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-40 right-20 w-24 h-24 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(15px)\",\n                            WebkitBackdropFilter: \"blur(15px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -25,\n                                25,\n                                -25\n                            ],\n                            x: [\n                                -10,\n                                10,\n                                -10\n                            ],\n                            rotate: [\n                                0,\n                                90,\n                                180\n                            ]\n                        },\n                        transition: {\n                            duration: 18,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-40 left-20 w-40 h-40 rounded-full opacity-10\",\n                        style: {\n                            background: \"rgba(124, 58, 237, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(124, 58, 237, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(124, 58, 237, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            animate: {\n                                y: [\n                                    -20,\n                                    20,\n                                    -20\n                                ],\n                                x: [\n                                    -10,\n                                    10,\n                                    -10\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i * 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.5\n                            },\n                            className: \"absolute w-2 h-2 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(10 + i * 12, \"%\"),\n                                top: \"\".concat(20 + i * 8, \"%\"),\n                                filter: \"blur(1px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"relative mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"w-24 h-24 mx-auto rounded-full flex items-center justify-center relative z-10\",\n                                whileHover: {\n                                    scale: 1.1,\n                                    rotate: 5\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                style: {\n                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)\",\n                                    backdropFilter: \"blur(20px)\",\n                                    WebkitBackdropFilter: \"blur(20px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.EnvelopeIcon, {\n                                        className: \"w-12 h-12 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.SparklesIcon, {\n                                        className: \"w-6 h-6 text-white absolute -top-2 -right-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"relative mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                    className: \"heading-lg text-white mb-4 relative z-10 px-8 py-4 text-arabic-premium\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    children: t(\"newsletter.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-2xl opacity-30\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)\",\n                                        backdropFilter: \"blur(30px)\",\n                                        WebkitBackdropFilter: \"blur(30px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                        boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                            variants: itemVariants,\n                            className: \"text-xl text-white/90 mb-16 max-w-3xl mx-auto text-center leading-relaxed text-arabic px-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            children: t(\"newsletter.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"max-w-lg mx-auto relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative p-8 rounded-3xl\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)\",\n                                        backdropFilter: \"blur(30px)\",\n                                        WebkitBackdropFilter: \"blur(30px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: \"0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...register(\"email\"),\n                                                        type: \"email\",\n                                                        placeholder: t(\"newsletter.placeholder\"),\n                                                        className: \"w-full px-6 py-4 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white/50 focus:outline-none transition-all duration-300 text-arabic \".concat(isRTL ? \"text-right\" : \"text-left\"),\n                                                        style: {\n                                                            background: \"rgba(255, 255, 255, 0.95)\",\n                                                            backdropFilter: \"blur(10px)\",\n                                                            WebkitBackdropFilter: \"blur(10px)\",\n                                                            border: \"1px solid rgba(255, 255, 255, 0.3)\"\n                                                        },\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm mt-2 text-start text-arabic\",\n                                                        children: t(\"common.validation.email\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                type: \"submit\",\n                                                disabled: isSubmitting,\n                                                whileHover: {\n                                                    scale: 1.02,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                className: \"w-full font-bold px-8 py-4 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 group text-arabic-premium\",\n                                                style: {\n                                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)\",\n                                                    color: \"#1e293b\",\n                                                    border: \"1px solid rgba(255, 255, 255, 0.3)\",\n                                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1)\"\n                                                },\n                                                children: [\n                                                    isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.PaperAirplaneIcon, {\n                                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isSubmitting ? t(\"common.status.loading\") : t(\"newsletter.submit\"),\n                                                    !isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.SparklesIcon, {\n                                                        className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/70 text-sm mt-6\",\n                                    children: isRTL ? \"لن نشارك بريدك الإلكتروني مع أي طرف ثالث. يمكنك إلغاء الاشتراك في أي وقت.\" : \"We won't share your email with anyone else. You can unsubscribe at any time.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCE7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"أخبار أسبوعية\" : \"Weekly Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"احصل على آخر الأخبار والفرص كل أسبوع\" : \"Get the latest news and opportunities every week\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"فرص حصرية\" : \"Exclusive Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"كن أول من يعلم بالفرص الجديدة\" : \"Be the first to know about new opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCA1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"نصائح مفيدة\" : \"Helpful Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"نصائح لتطوير مهاراتك وزيادة دخلك\" : \"Tips to develop your skills and increase your income\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(Newsletter, \"apw5LViUSGeGus4dp0ePtWuFBDU=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = Newsletter;\nvar _c;\n$RefreshReg$(_c, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/Newsletter.tsx\n"));

/***/ })

});