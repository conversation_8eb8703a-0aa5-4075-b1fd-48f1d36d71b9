"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/Newsletter.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Newsletter.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Newsletter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"../../node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EnvelopeIcon,PaperAirplaneIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Validation schema\nconst newsletterSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email(\"Invalid email address\")\n});\nfunction Newsletter() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(newsletterSchema)\n    });\n    const onSubmit = async (_data)=>{\n        setIsSubmitting(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // In a real app, you would send the email to your newsletter service\n            // TODO: Send email to newsletter service\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(t(\"newsletter.success\"));\n            reset();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(t(\"newsletter.error\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"newsletter\",\n        ref: ref,\n        className: \"section-padding relative overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(206, 17, 38, 0.15) 0%, transparent 60%),\\n          radial-gradient(circle at \").concat(100 - mousePosition.x, \"% \").concat(100 - mousePosition.y, \"%, rgba(0, 122, 61, 0.12) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -30,\n                                30,\n                                -30\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 w-32 h-32 rounded-full opacity-20\",\n                        style: {\n                            background: \"rgba(206, 17, 38, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            WebkitBackdropFilter: \"blur(20px)\",\n                            border: \"1px solid rgba(206, 17, 38, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(206, 17, 38, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                40,\n                                -40,\n                                40\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-40 right-20 w-24 h-24 rounded-full opacity-15\",\n                        style: {\n                            background: \"rgba(0, 122, 61, 0.1)\",\n                            backdropFilter: \"blur(15px)\",\n                            WebkitBackdropFilter: \"blur(15px)\",\n                            border: \"1px solid rgba(0, 122, 61, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(0, 122, 61, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        animate: {\n                            y: [\n                                -25,\n                                25,\n                                -25\n                            ],\n                            x: [\n                                -10,\n                                10,\n                                -10\n                            ],\n                            rotate: [\n                                0,\n                                90,\n                                180\n                            ]\n                        },\n                        transition: {\n                            duration: 18,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-40 left-20 w-40 h-40 rounded-full opacity-10\",\n                        style: {\n                            background: \"rgba(124, 58, 237, 0.1)\",\n                            backdropFilter: \"blur(25px)\",\n                            WebkitBackdropFilter: \"blur(25px)\",\n                            border: \"1px solid rgba(124, 58, 237, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(124, 58, 237, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            animate: {\n                                y: [\n                                    -20,\n                                    20,\n                                    -20\n                                ],\n                                x: [\n                                    -10,\n                                    10,\n                                    -10\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i * 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.5\n                            },\n                            className: \"absolute w-2 h-2 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(10 + i * 12, \"%\"),\n                                top: \"\".concat(20 + i * 8, \"%\"),\n                                filter: \"blur(1px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: inView ? \"visible\" : \"hidden\",\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"w-20 h-20 mx-auto mb-8 bg-white/20 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.EnvelopeIcon, {\n                                className: \"w-10 h-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                            variants: itemVariants,\n                            className: \"heading-lg mb-4 text-white\",\n                            children: t(\"newsletter.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                            variants: itemVariants,\n                            className: \"text-xl text-white/90 mb-12 max-w-2xl mx-auto\",\n                            children: t(\"newsletter.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"email\"),\n                                                    type: \"email\",\n                                                    placeholder: t(\"newsletter.placeholder\"),\n                                                    className: \"w-full px-6 py-4 rounded-lg bg-white/95 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white focus:outline-none transition-all duration-200 \".concat(isRTL ? \"text-right\" : \"text-left\"),\n                                                    disabled: isSubmitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/90 text-sm mt-2 text-start\",\n                                                    children: t(\"common.validation.email\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"w-full bg-white text-primary-600 hover:bg-gray-50 font-medium px-6 py-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2\",\n                                            children: [\n                                                isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.PaperAirplaneIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isSubmitting ? t(\"common.status.loading\") : t(\"newsletter.submit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/70 text-sm mt-6\",\n                                    children: isRTL ? \"لن نشارك بريدك الإلكتروني مع أي طرف ثالث. يمكنك إلغاء الاشتراك في أي وقت.\" : \"We won't share your email with anyone else. You can unsubscribe at any time.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCE7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"أخبار أسبوعية\" : \"Weekly Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"احصل على آخر الأخبار والفرص كل أسبوع\" : \"Get the latest news and opportunities every week\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"فرص حصرية\" : \"Exclusive Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"كن أول من يعلم بالفرص الجديدة\" : \"Be the first to know about new opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDCA1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: isRTL ? \"نصائح مفيدة\" : \"Helpful Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: isRTL ? \"نصائح لتطوير مهاراتك وزيادة دخلك\" : \"Tips to develop your skills and increase your income\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(Newsletter, \"apw5LViUSGeGus4dp0ePtWuFBDU=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_7__.useInView,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useForm\n    ];\n});\n_c = Newsletter;\nvar _c;\n$RefreshReg$(_c, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/Newsletter.tsx\n"));

/***/ })

});